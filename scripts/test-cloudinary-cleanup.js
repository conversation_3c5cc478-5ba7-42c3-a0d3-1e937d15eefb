#!/usr/bin/env node

/**
 * Test script for Cloudinary cleanup functionality
 * Run with: node scripts/test-cloudinary-cleanup.js
 */

const { cleanupIncompleteUploads, trackIncompleteUpload, markUploadComplete } = require('../lib/cloudinary');

async function testCleanupFunctionality() {
  console.log('🧪 Testing Cloudinary cleanup functionality...\n');

  try {
    // Test 1: Track some fake incomplete uploads
    console.log('1. Testing upload tracking...');
    trackIncompleteUpload('test-public-id-1', 'test-context-1', 'positive7/test');
    trackIncompleteUpload('test-public-id-2', 'test-context-2', 'positive7/test');
    trackIncompleteUpload('test-public-id-3', 'test-context-3', 'positive7/test');
    console.log('✅ Tracked 3 test uploads\n');

    // Test 2: Mark one as complete
    console.log('2. Testing upload completion...');
    markUploadComplete('test-public-id-2');
    console.log('✅ Marked one upload as complete\n');

    // Test 3: Try cleanup (should find 2 incomplete uploads but won't actually delete since they're fake)
    console.log('3. Testing cleanup (dry run with fake uploads)...');
    const result = await cleanupIncompleteUploads(0); // 0 minutes = cleanup everything
    console.log(`📊 Cleanup result: ${result.cleaned} cleaned, ${result.errors.length} errors`);
    
    if (result.errors.length > 0) {
      console.log('Expected errors (since these are fake public IDs):');
      result.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }
    console.log('✅ Cleanup function executed\n');

    // Test 4: Test with no incomplete uploads
    console.log('4. Testing cleanup with no incomplete uploads...');
    const emptyResult = await cleanupIncompleteUploads(30);
    console.log(`📊 Empty cleanup result: ${emptyResult.cleaned} cleaned, ${emptyResult.errors.length} errors`);
    console.log('✅ Empty cleanup handled correctly\n');

    console.log('🎉 All tests completed successfully!');
    console.log('\n📝 Summary:');
    console.log('- Upload tracking: ✅ Working');
    console.log('- Upload completion marking: ✅ Working');
    console.log('- Cleanup function: ✅ Working');
    console.log('- Error handling: ✅ Working');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
testCleanupFunctionality();
