# Cloudinary Cleanup System

This document describes the enhanced Cloudinary cleanup system that prevents orphaned images and provides comprehensive logging for all Cloudinary operations.

## Overview

The system addresses three main issues:
1. **Upload button visibility** - Made upload buttons more prominent when no image is present
2. **Incomplete upload cleanup** - Automatically cleans up images uploaded but never saved to database
3. **Enhanced logging** - Comprehensive logging for all Cloudinary operations

## Features

### 1. Enhanced Upload Button Styling

**Problem**: Upload buttons in forms were almost invisible when no image was uploaded.

**Solution**: 
- Dynamic styling based on image presence
- Blue prominent button when no image exists
- Subtle overlay button when image is present

**Files Modified**:
- `app/admin/trips/components/Trip-FormCompleted.tsx`
- `components/ui/CloudinaryUpload.tsx`

### 2. Incomplete Upload Tracking & Cleanup

**Problem**: Images uploaded to Cloudinary but never saved to database become orphaned.

**Solution**: 
- Track uploads as "incomplete" immediately after Cloudinary upload
- Mark as "complete" after successful database save
- Periodic cleanup of incomplete uploads older than specified time

**Key Functions**:
```javascript
// Track upload as incomplete
trackIncompleteUpload(publicId, context, folder)

// Mark upload as complete
markUploadComplete(publicId)

// Cleanup incomplete uploads
cleanupIncompleteUploads(olderThanMinutes)
```

### 3. Enhanced Logging

**Problem**: Limited visibility into Cloudinary operations for debugging and monitoring.

**Solution**: 
- Comprehensive logging with context and timing
- Success/failure indicators with emojis
- Performance metrics (duration tracking)
- Contextual information (operation type, entity)

**Log Examples**:
```
✅ File uploaded successfully: positive7/trips/abc123 (1.2MB)
🗑️ Cleaned up incomplete upload: positive7/trips/xyz789 (trip-context)
❌ Cloudinary delete error for positive7/blog/def456 (500ms): Not found
```

## API Endpoints

### Cleanup Endpoint
```
POST /api/admin/cloudinary/cleanup
{
  "olderThanMinutes": 30
}
```

### Cleanup Status
```
GET /api/admin/cloudinary/cleanup
```

## Integration Points

### Upload Flow
1. User uploads image → Cloudinary
2. `trackIncompleteUpload()` called with public ID
3. User saves form → Database
4. `markUploadComplete()` called with public ID
5. Upload removed from incomplete tracking

### Delete/Replace Flow
1. User deletes/replaces image
2. `deleteFromCloudinary()` called with context
3. Enhanced logging captures operation details

## Scheduled Cleanup

### Manual Cleanup
```javascript
import { runScheduledCleanup } from '@/lib/cloudinary-cleanup-scheduler';

// Cleanup uploads older than 1 hour
await runScheduledCleanup(60);
```

### Tiered Cleanup
```javascript
import { runTieredCleanup } from '@/lib/cloudinary-cleanup-scheduler';

// Run cleanup with multiple age thresholds
await runTieredCleanup();
// - 30 minutes (recent)
// - 2 hours (medium)
// - 12 hours (old)
```

## Configuration

### Default Settings
- **Cleanup Age**: 30 minutes for incomplete uploads
- **Max Cleanup Age**: 24 hours (1440 minutes)
- **Concurrency**: Parallel cleanup with Promise.all
- **Batch Size**: 100 images per Cloudinary API call

### Environment Variables
```env
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloud_name
NEXT_PUBLIC_CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
```

## Testing

### Test Script
```bash
node scripts/test-cloudinary-cleanup.js
```

### Manual Testing
1. Upload image in admin form
2. Don't save form (close browser/navigate away)
3. Wait 30+ minutes
4. Run cleanup endpoint
5. Verify image is deleted from Cloudinary

## Monitoring

### Log Patterns to Monitor
- `📝 Tracking incomplete upload:` - New incomplete uploads
- `✅ Marked upload as complete:` - Successful form saves
- `🧹 Cleanup completed:` - Cleanup operations
- `❌ Cloudinary delete error:` - Failed deletions

### Metrics to Track
- Number of incomplete uploads over time
- Cleanup success/failure rates
- Average upload-to-completion time
- Orphaned image count trends

## Production Considerations

### Memory Usage
- In-memory tracking suitable for single-instance deployments
- For multi-instance/serverless, consider Redis or database storage

### Cleanup Frequency
- Recommended: Every 30-60 minutes
- Can be triggered via cron jobs or serverless functions
- Consider off-peak hours for large cleanups

### Error Handling
- Cleanup continues even if individual deletions fail
- Failed deletions are logged but don't stop the process
- Database operations proceed even if Cloudinary cleanup fails

## Files Modified/Added

### Core Files
- `lib/cloudinary.ts` - Enhanced with tracking and logging
- `app/api/cloudinary/upload/route.ts` - Added tracking
- `lib/cloudinary-cleanup-scheduler.ts` - Scheduled cleanup functions

### API Routes Updated
- `app/api/admin/trips/[id]/route.ts`
- `app/api/admin/trips/route.ts`
- `app/api/admin/blogs/[id]/route.ts`
- `app/api/admin/blogs/route.ts`
- `app/api/admin/trips-photos/[id]/route.ts`
- `app/api/admin/trips-photos/route.ts`

### New Files
- `app/api/admin/cloudinary/cleanup/route.ts` - Cleanup endpoint
- `scripts/test-cloudinary-cleanup.js` - Test script
- `docs/CLOUDINARY_CLEANUP_SYSTEM.md` - This documentation
