export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      admin_profiles: {
        Row: {
          id: string
          username: string | null
          full_name: string | null
          avatar_url: string | null
          is_active: boolean
          last_login_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          username?: string | null
          full_name?: string | null
          avatar_url?: string | null
          is_active?: boolean
          last_login_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          username?: string | null
          full_name?: string | null
          avatar_url?: string | null
          is_active?: boolean
          last_login_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "admin_profiles_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      admin_roles: {
        Row: {
          id: string
          name: string
          description: string | null
          permissions: Record<string, string[]>
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          permissions?: Record<string, string[]>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          permissions?: Record<string, string[]>
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      admin_user_roles: {
        Row: {
          id: string
          user_id: string
          role_id: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          role_id: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          role_id?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "admin_user_roles_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "admin_user_roles_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "admin_roles"
            referencedColumns: ["id"]
          }
        ]
      }
      admin_sessions: {
        Row: {
          created_at: string | null
          expires_at: string
          id: string
          token: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          expires_at: string
          id?: string
          token: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          expires_at?: string
          id?: string
          token?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "admin_sessions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "admin_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      blog_posts: {
        Row: {
          author_id: string | null
          category: string | null
          content: string
          created_at: string | null
          excerpt: string | null
          featured_image_url: string | null
          id: string
          is_published: boolean | null
          published_at: string | null
          seo_description: string | null
          seo_title: string | null
          slug: string
          tags: string[] | null
          title: string
          updated_at: string | null
        }
        Insert: {
          author_id?: string | null
          category?: string | null
          content: string
          created_at?: string | null
          excerpt?: string | null
          featured_image_url?: string | null
          id?: string
          is_published?: boolean | null
          published_at?: string | null
          seo_description?: string | null
          seo_title?: string | null
          slug: string
          tags?: string[] | null
          title: string
          updated_at?: string | null
        }
        Update: {
          author_id?: string | null
          category?: string | null
          content?: string
          created_at?: string | null
          excerpt?: string | null
          featured_image_url?: string | null
          id?: string
          is_published?: boolean | null
          published_at?: string | null
          seo_description?: string | null
          seo_title?: string | null
          slug?: string
          tags?: string[] | null
          title?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      inquiries: {
        Row: {
          admin_notes: string | null
          created_at: string | null
          email: string
          id: string
          inquiry_type: string | null
          message: string
          name: string
          phone: string | null
          responded_at: string | null
          status: Database["public"]["Enums"]["inquiry_status"] | null
          subject: string | null
          trip_id: string | null
          updated_at: string | null
        }
        Insert: {
          admin_notes?: string | null
          created_at?: string | null
          email: string
          id?: string
          inquiry_type?: string | null
          message: string
          name: string
          phone?: string | null
          responded_at?: string | null
          status?: Database["public"]["Enums"]["inquiry_status"] | null
          subject?: string | null
          trip_id?: string | null
          updated_at?: string | null
        }
        Update: {
          admin_notes?: string | null
          created_at?: string | null
          email?: string
          id?: string
          inquiry_type?: string | null
          message?: string
          name?: string
          phone?: string | null
          responded_at?: string | null
          status?: Database["public"]["Enums"]["inquiry_status"] | null
          subject?: string | null
          trip_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "inquiries_trip_id_fkey"
            columns: ["trip_id"]
            isOneToOne: false
            referencedRelation: "trips"
            referencedColumns: ["id"]
          },
        ]
      }
      security_audit_logs: {
        Row: {
          id: string
          event_type: string
          severity: string
          user_id: string | null
          user_email: string | null
          ip_address: string | null
          user_agent: string | null
          resource_type: string | null
          resource_id: string | null
          action: string
          description: string
          metadata: Json | null
          timestamp: string
          session_id: string | null
          request_id: string | null
          success: boolean
          error_message: string | null
        }
        Insert: {
          id?: string
          event_type: string
          severity: string
          user_id?: string | null
          user_email?: string | null
          ip_address?: string | null
          user_agent?: string | null
          resource_type?: string | null
          resource_id?: string | null
          action: string
          description: string
          metadata?: Json | null
          timestamp?: string
          session_id?: string | null
          request_id?: string | null
          success?: boolean
          error_message?: string | null
        }
        Update: {
          id?: string
          event_type?: string
          severity?: string
          user_id?: string | null
          user_email?: string | null
          ip_address?: string | null
          user_agent?: string | null
          resource_type?: string | null
          resource_id?: string | null
          action?: string
          description?: string
          metadata?: Json | null
          timestamp?: string
          session_id?: string | null
          request_id?: string | null
          success?: boolean
          error_message?: string | null
        }
        Relationships: []
      }
      team_members: {
        Row: {
          bio: string
          created_at: string | null
          id: string
          image_url: string | null
          is_active: boolean | null
          name: string
          position: string
          sort_order: number | null
          updated_at: string | null
        }
        Insert: {
          bio: string
          created_at?: string | null
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          name: string
          position: string
          sort_order?: number | null
          updated_at?: string | null
        }
        Update: {
          bio?: string
          created_at?: string | null
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          name?: string
          position?: string
          sort_order?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      trip_photos_details: {
        Row: {
          access_password_hash: string | null
          created_at: string | null
          featured_image_url: string | null
          google_photos_album_id: string | null
          id: string
          manual_shareable_url: string | null
          oauth_refresh_token: string | null
          oauth_refresh_token_encrypted: string | null
          oauth_user_email: string | null
          security_version: number | null
          storage_type: string | null
          trip_description: string | null
          trip_name: string
          updated_at: string | null
        }
        Insert: {
          access_password_hash?: string | null
          created_at?: string | null
          featured_image_url?: string | null
          google_photos_album_id?: string | null
          id?: string
          manual_shareable_url?: string | null
          oauth_refresh_token?: string | null
          oauth_refresh_token_encrypted?: string | null
          oauth_user_email?: string | null
          security_version?: number | null
          storage_type?: string | null
          trip_description?: string | null
          trip_name: string
          updated_at?: string | null
        }
        Update: {
          access_password_hash?: string | null
          created_at?: string | null
          featured_image_url?: string | null
          google_photos_album_id?: string | null
          id?: string
          manual_shareable_url?: string | null
          oauth_refresh_token?: string | null
          oauth_refresh_token_encrypted?: string | null
          oauth_user_email?: string | null
          security_version?: number | null
          storage_type?: string | null
          trip_description?: string | null
          trip_name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      galleries: {
        Row: {
          id: string
          name: string
          description: string | null
          trip_id: string | null
          folder_name: string | null
          is_active: boolean
          featured_image_url: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          trip_id?: string | null
          folder_name?: string | null
          is_active?: boolean
          featured_image_url?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          trip_id?: string | null
          folder_name?: string | null
          is_active?: boolean
          featured_image_url?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "galleries_trip_id_fkey"
            columns: ["trip_id"]
            isOneToOne: false
            referencedRelation: "trips"
            referencedColumns: ["id"]
          }
        ]
      }
      gallery_images: {
        Row: {
          id: string
          gallery_id: string
          image_url: string
          cloudinary_public_id: string
          order_index: number | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          gallery_id: string
          image_url: string
          cloudinary_public_id: string
          order_index?: number | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          gallery_id?: string
          image_url?: string
          cloudinary_public_id?: string
          order_index?: number | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "gallery_images_gallery_id_fkey"
            columns: ["gallery_id"]
            isOneToOne: false
            referencedRelation: "galleries"
            referencedColumns: ["id"]
          }
        ]
      }
      trips: {
        Row: {
          activities: string[] | null
          auto_deactivation_date: string | null
          available_dates: string[] | null
          benefits: string[] | null
          cancellation_policy: Json | null
          category: string | null
          commercial_price: number | null
          created_at: string | null
          days: number
          description: string | null
          destination: string
          detailed_description: string | null
          difficulty: Database["public"]["Enums"]["trip_difficulty"]
          drop_location: string | null
          exclusions: string[] | null
          featured_image_url: string | null
          id: string
          inclusions: string[] | null
          is_active: boolean | null
          is_featured: boolean | null
          is_trek: boolean | null
          itinerary: Json | null
          max_age: number | null
          min_age: number | null
          mode_of_travel: string | null
          nights: number
          optional_activities: string[] | null
          payment_terms: string | null
          pickup_location: string | null
          price_per_person: number
          property_used: string | null
          safety_supervision: string[] | null
          slug: string
          special_notes: string[] | null
          things_to_carry: string[] | null
          title: string
          updated_at: string | null
        }
        Insert: {
          activities?: string[] | null
          auto_deactivation_date?: string | null
          available_dates?: string[] | null
          benefits?: string[] | null
          cancellation_policy?: Json | null
          category?: string | null
          commercial_price?: number | null
          created_at?: string | null
          days?: number
          description?: string | null
          destination: string
          detailed_description?: string | null
          difficulty: Database["public"]["Enums"]["trip_difficulty"]
          drop_location?: string | null
          exclusions?: string[] | null
          featured_image_url?: string | null
          id?: string
          inclusions?: string[] | null
          is_active?: boolean | null
          is_featured?: boolean | null
          is_trek?: boolean | null
          itinerary?: Json | null
          max_age?: number | null
          min_age?: number | null
          mode_of_travel?: string | null
          nights?: number
          optional_activities?: string[] | null
          payment_terms?: string | null
          pickup_location?: string | null
          price_per_person: number
          property_used?: string | null
          safety_supervision?: string[] | null
          slug: string
          special_notes?: string[] | null
          things_to_carry?: string[] | null
          title: string
          updated_at?: string | null
        }
        Update: {
          activities?: string[] | null
          auto_deactivation_date?: string | null
          available_dates?: string[] | null
          benefits?: string[] | null
          cancellation_policy?: Json | null
          category?: string | null
          commercial_price?: number | null
          created_at?: string | null
          days?: number
          description?: string | null
          destination?: string
          detailed_description?: string | null
          difficulty?: Database["public"]["Enums"]["trip_difficulty"]
          drop_location?: string | null
          exclusions?: string[] | null
          featured_image_url?: string | null
          id?: string
          inclusions?: string[] | null
          is_active?: boolean | null
          is_featured?: boolean | null
          is_trek?: boolean | null
          itinerary?: Json | null
          max_age?: number | null
          min_age?: number | null
          mode_of_travel?: string | null
          nights?: number
          optional_activities?: string[] | null
          payment_terms?: string | null
          pickup_location?: string | null
          price_per_person?: number
          property_used?: string | null
          safety_supervision?: string[] | null
          slug?: string
          special_notes?: string[] | null
          things_to_carry?: string[] | null
          title?: string
          updated_at?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      citext: {
        Args: { "": boolean } | { "": string } | { "": unknown }
        Returns: string
      }
      citext_hash: {
        Args: { "": string }
        Returns: number
      }
      citextin: {
        Args: { "": unknown }
        Returns: string
      }
      citextout: {
        Args: { "": string }
        Returns: unknown
      }
      citextrecv: {
        Args: { "": unknown }
        Returns: string
      }
      citextsend: {
        Args: { "": string }
        Returns: string
      }
      cleanup_security_audit_logs: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      create_admin_profile: {
        Args: { user_id: string }
        Returns: undefined
      }
      deactivate_expired_trips: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      deactivate_expired_trips_with_details: {
        Args: Record<PropertyKey, never>
        Returns: {
          trip_id: string
          trip_title: string
          destination: string
          days: number
          nights: number
          price_per_person: number
          auto_deactivation_date: string
          deactivated_at: string
        }[]
      }
      delete_admin_session: {
        Args: { token_param: string }
        Returns: undefined
      }
      get_deactivation_status: {
        Args: Record<PropertyKey, never>
        Returns: {
          cron_job_exists: boolean
          trips_with_auto_deactivation: number
          active_trips_with_auto_deactivation: number
          expired_trips_count: number
        }[]
      }
      schedule_trip_deactivation: {
        Args: { job_name?: string; cron_schedule?: string }
        Returns: Json
      }
      update_trip_deactivation_date: {
        Args: { trip_id: string; new_date: string }
        Returns: Json
      }
      insert_security_audit_log: {
        Args: {
          p_event_type: string
          p_severity: string
          p_user_id?: string
          p_user_email?: string
          p_ip_address?: string
          p_user_agent?: string
          p_resource_type?: string
          p_resource_id?: string
          p_action: string
          p_description: string
          p_metadata?: Json
          p_session_id?: string
          p_request_id?: string
          p_success?: boolean
          p_error_message?: string
        }
        Returns: string
      }
      insert_trip_photo: {
        Args: { p_image_url: string; p_is_watermarked?: boolean }
        Returns: string
      }
      query_security_audit_logs: {
        Args: {
          p_limit?: number
          p_offset?: number
          p_event_type?: string
          p_severity?: string
          p_user_id?: string
          p_start_date?: string
          p_end_date?: string
        }
        Returns: {
          id: string
          event_type: string
          severity: string
          user_id: string | null
          user_email: string | null
          ip_address: string | null
          user_agent: string | null
          resource_type: string | null
          resource_id: string | null
          action: string
          description: string
          metadata: Json | null
          timestamp: string
          session_id: string | null
          request_id: string | null
          success: boolean
          error_message: string | null
        }[]
      }
      sync_user_role_to_auth: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      update_updated_at_column: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      get_cron_job_details: {
        Args: Record<PropertyKey, never>
        Returns: {
          job_id: number
          job_name: string
          schedule: string
          command: string
          active: boolean
          last_run_started_at: string | null
          last_run_status: string
        }[]
      }
    }
    Enums: {
      booking_status: "pending" | "confirmed" | "cancelled" | "completed"
      inquiry_status: "new" | "in_progress" | "resolved" | "closed"
      storage_type_enum: "google_photos" | "google_photos_oauth"
      trip_difficulty: "easy" | "moderate" | "challenging" | "extreme"
      user_role: "customer" | "admin"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}