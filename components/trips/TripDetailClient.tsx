'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  ArrowLeft,
  Heart,
  Share2,
  Mountain,
  CheckCircle,
  XCircle,
  Info,
  Shield,
  Package,
  Activity,
  Train,
  AlertTriangle,
  Backpack,
  FileText,
  Bed,
  Truck,
  Bike,
  Navigation,
  Map,
  ChevronRight,
  ChevronDown,
  ChevronUp,
  RefreshCw,
  Home,
  Phone,
  DollarSign,
  Tag
} from 'lucide-react';
import Button from '@/components/ui/Button';
import { cn } from '@/lib/utils';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { JsonLd } from '@/components/seo/JsonLd';
import { TransportModeIcon } from './TransportModeIcon';
import { usePublicTrip } from '@/hooks/useTrips';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { ClientTrip } from '@/types/client-trip';
import DepartureDateCalendar from '@/components/trips/DepartureDateCalendar';


// Define types for itinerary details
interface ItineraryDetails {
  title: string;
  description: string;
  location?: string;
  accommodation?: string;
  meals?: string;
}

// Define tab interface
interface Tab {
  id: string;
  label: string;
  icon?: React.ReactNode;
}

// Trip interface imported from @/types/database

interface TripDetailClientProps {
  tripSlug: string;
  initialTrip?: ClientTrip;
  relatedTrips: ClientTrip[];
}

export default function TripDetailClient({ tripSlug, initialTrip, relatedTrips }: TripDetailClientProps) {
  // Use React Query to fetch trip data with real-time updates
  const { data: trip, isLoading, error, refetch } = usePublicTrip(tripSlug, true);

  // Enhanced logic to handle real-time deletions/deactivations
  const currentTrip = trip || initialTrip;

  // Check if item was deleted/deactivated during real-time updates
  const isItemDeleted = error && (
    (error as any)?.status === 404 ||
    (error as any)?.message?.includes('404') ||
    (error as any)?.message?.includes('not found')
  ) && initialTrip;

  // Remove unused isItemUnavailable - logic is already handled by isItemDeleted
  const [activeTab, setActiveTab] = useState('travel-info');
  const [expandedItineraryDays, setExpandedItineraryDays] = useState<Set<number>>(new Set([1]));

  // Tabs configuration with icons
  const tabs: Tab[] = [
    { id: 'travel-info', label: 'Travel Info', icon: <Train className="w-5 h-5" /> },
    { id: 'inclusions', label: 'Inclusions', icon: <CheckCircle className="w-5 h-5" /> },
    { id: 'activities', label: 'Activities', icon: <Activity className="w-5 h-5" /> },
    { id: 'things-to-carry', label: 'Things to Carry', icon: <Backpack className="w-5 h-5" /> },
    { id: 'safety', label: 'Safety & Benefits', icon: <Shield className="w-5 h-5" /> },
  ];

  // Helper function to check if an array has items
  const hasLength = (arr?: any[] | null): boolean => {
    return !!arr && arr.length > 0;
  };

  // Function to check if a tab has content
  const hasTabContent = (tabId: string) => {
    if (!currentTrip) return false;

    switch (tabId) {
      case 'travel-info':
        return !!(currentTrip.mode_of_travel || currentTrip.pickup_location || currentTrip.drop_location || currentTrip.property_used);
      case 'inclusions':
        return (currentTrip.inclusions && currentTrip.inclusions.length > 0) || (currentTrip.exclusions && currentTrip.exclusions.length > 0);
      case 'activities':
        return (currentTrip.activities && currentTrip.activities.length > 0) || (currentTrip.optional_activities && currentTrip.optional_activities.length > 0);
      case 'things-to-carry':
        return hasLength(currentTrip.things_to_carry);
      case 'safety':
        return hasLength(currentTrip.benefits) || hasLength(currentTrip.safety_supervision);
      default:
        return false;
    }
  };

  // Set initial active tab to the first one that has content
  useEffect(() => {
    if (currentTrip) {
      for (const tab of tabs) {
        if (hasTabContent(tab.id)) {
          setActiveTab(tab.id);
          break;
        }
      }
    }
  }, [currentTrip]); // Add currentTrip as dependency

  // Loading state
  if (isLoading && !currentTrip) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  // Handle case where trip was deleted/deactivated during real-time updates
  if (isItemDeleted) {
    return (
      <div className="text-center py-12">
        <div className="text-orange-400 mb-4">
          <AlertTriangle className="w-16 h-16 mx-auto" />
        </div>
        <h3 className="text-xl font-medium text-gray-900 mb-2">Trip No Longer Available</h3>
        <p className="text-gray-600 mb-6">
          This trip has been removed or is no longer active. It may have been deleted by an administrator or temporarily deactivated.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/trips">
            <button className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Browse All Trips
            </button>
          </Link>
          <button
            onClick={() => refetch()}
            className="inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Check Again
          </button>
        </div>
      </div>
    );
  }

  // Error state
  if (error && !currentTrip) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <Mountain className="w-16 h-16 mx-auto" />
        </div>
        <h3 className="text-xl font-medium text-gray-900 mb-2">Failed to load trip</h3>
        <p className="text-gray-600 mb-4">
          There was an error loading this trip. Please try again.
        </p>
        <button
          onClick={() => refetch()}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Try Again
        </button>
      </div>
    );
  }

  // Not found state
  if (!currentTrip) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <Mountain className="w-16 h-16 mx-auto" />
        </div>
        <h3 className="text-xl font-medium text-gray-900 mb-2">Trip not found</h3>
        <p className="text-gray-600">
          This trip could not be found or may have been removed.
        </p>
      </div>
    );
  }

  // Function to toggle itinerary day dropdown
  const toggleItineraryDay = (day: number) => {
    const newExpanded = new Set(expandedItineraryDays);
    if (newExpanded.has(day)) {
      newExpanded.delete(day);
    } else {
      newExpanded.add(day);
    }
    setExpandedItineraryDays(newExpanded);
  };
  
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  // Tab content animation variants
  const tabContentVariants = {
    hidden: { opacity: 0, x: 10 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.3 } }
  };




  // Format price to Indian Rupees
  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(price);
  };

  // Utility function to get difficulty color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'bg-green-100 text-green-800';
      case 'moderate':
        return 'bg-yellow-100 text-yellow-800';
      case 'challenging':
        return 'bg-orange-100 text-orange-800';
      case 'extreme':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  // Create the structured data for JSON-LD
  const tourData = currentTrip ? {
    "@context": "https://schema.org",
    "@type": "TouristTrip",
    "name": currentTrip.title,
    "description": currentTrip.description,
    "touristType": ["Educational", currentTrip.is_trek ? "Adventure" : "Leisure"],
    "offers": {
      "@type": "Offer",
      "price": currentTrip.price_per_person,
      "priceCurrency": "INR",
      "availability": "https://schema.org/InStock"
    },
    "itinerary": {
      "@type": "ItemList",
      "numberOfItems": currentTrip.days,
      "itemListElement": Object.entries(currentTrip.itinerary || {}).map(([_, details], index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": "TouristDestination",
          "name": (details as any)?.title || `Day ${index + 1}`,
          "description": (details as any)?.description || ""
        }
      }))
    },
    "subjectOf": {
      "@type": "CreativeWork",
      "abstract": currentTrip.detailed_description
    },
    "provider": {
      "@type": "Organization",
      "name": "Positive7 Educational Tours",
      "url": "https://positive7.org"
    }
  } : null;

  return (
    <>
      {/* Add JSON-LD structured data */}
      {tourData && <JsonLd data={tourData} />}

      <div className="flex-1">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="min-h-screen bg-gradient-to-br from-coral-50 via-orange-50 to-teal-50"
        >
          {/* Hero Section - Full viewport width */}
          <motion.section variants={itemVariants} className="relative h-[60vh] overflow-hidden w-full">
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-gradient-to-br from-coral-500/10 via-orange-500/5 to-teal-500/10 z-10"></div>

            <Image
              src={currentTrip.featured_image_url || '/images/fallback-currentTrip.jpg'}
              alt={currentTrip.title}
              fill
              className="object-cover w-full transition-transform duration-700 hover:scale-105"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-coral-500/20 z-20" />

            {/* Navigation */}
            <div className="absolute top-6 left-6 z-30">
              <Link href="/trips">
                <Button variant="secondary" size="sm" className="bg-white/20 backdrop-blur-md hover:bg-white/30 border border-white/20 shadow-lg">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Trips
                </Button>
              </Link>
            </div>

            {/* Action Buttons */}
            <div className="absolute top-6 right-6 z-30 flex gap-2">
              <Button variant="secondary" size="sm" className="bg-white/20 backdrop-blur-md hover:bg-white/30 border border-white/20 shadow-lg">
                <Heart className="w-4 h-4" />
              </Button>
              <Button variant="secondary" size="sm" className="bg-white/20 backdrop-blur-md hover:bg-white/30 border border-white/20 shadow-lg">
                <Share2 className="w-4 h-4" />
              </Button>
            </div>

            {/* Hero Content */}
            <div className="absolute bottom-0 left-0 right-0 p-8 text-white z-30">
              <div className="max-w-7xl mx-auto">
                <motion.div
                  variants={itemVariants}
                  className="flex flex-wrap items-end justify-between gap-6"
                >
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-4 mb-4">
                      <span className="px-4 py-2 bg-gradient-to-r from-coral-500 to-orange-500 rounded-full text-sm font-medium shadow-lg">
                        {currentTrip.destination}
                      </span>
                    </div>
                    <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-display font-black mb-2 bg-gradient-to-r from-white via-coral-200 to-orange-200 bg-clip-text text-transparent">{currentTrip.title}</h1>
                    <p className="text-lg sm:text-xl text-white/90 mb-4">{currentTrip.destination}</p>
                    <div className="flex flex-wrap items-center gap-6 text-white/80">
                      <div className="flex items-center gap-2">
                        <Clock className="w-5 h-5" />
                        <span>{currentTrip.days} Days, {currentTrip.nights} Nights</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="w-5 h-5" />
                        <span>Educational Tour</span>
                      </div>
                      {currentTrip.is_trek && (
                        <div className="flex items-center gap-2 bg-green-600/80 px-2 py-1 rounded-md">
                          <Mountain className="w-4 h-4" />
                          <span>Trek</span>
                        </div>
                      )}
                      <div className="flex items-center gap-2">
                        <Mountain className="w-5 h-5" />
                        <span className="capitalize">{currentTrip.difficulty}</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.section>

          {/* Main Content */}
          <div className="max-w-7xl mx-auto px-4 py-12">
            <div className="grid lg:grid-cols-3 gap-12">
              {/* Left Column - Main Content - Scrolls with page */}
              <div className="lg:col-span-2 space-y-12 lg:pr-4">
                {/* Trip Overview */}
                <motion.section variants={itemVariants} className="card-modern p-8">
                  <h2 className="text-3xl font-bold mb-6 text-gray-900">Trip Overview</h2>
                  <p className="text-gray-700 text-lg leading-relaxed mb-8">
                    {currentTrip.description || 'Discover amazing educational experiences and create unforgettable memories with Positive7.'}
                  </p>

                  {/* Quick Info Grid */}
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <Clock className="w-5 h-5 text-blue-600" />
                        <div>
                          <div className="font-medium text-gray-900">Duration</div>
                          <div className="text-gray-600">{currentTrip.days} Days, {currentTrip.nights} Nights</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Users className="w-5 h-5 text-blue-600" />
                        <div>
                          <div className="font-medium text-gray-900">Age Range</div>
                          <div className="text-gray-600">
                            {currentTrip.min_age && currentTrip.max_age 
                              ? `${currentTrip.min_age} - ${currentTrip.max_age} years` 
                              : currentTrip.min_age 
                                ? `${currentTrip.min_age}+ years` 
                                : currentTrip.max_age 
                                  ? `Up to ${currentTrip.max_age} years` 
                                  : 'All ages'}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <Mountain className="w-5 h-5 text-blue-600" />
                        <div>
                          <div className="font-medium text-gray-900">Difficulty</div>
                          <div className="text-gray-600 capitalize">{currentTrip.difficulty}</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <MapPin className="w-5 h-5 text-blue-600" />
                        <div>
                          <div className="font-medium text-gray-900">Destination</div>
                          <div className="text-gray-600">{currentTrip.destination}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.section>

                {/* Trip Details */}
                {currentTrip.detailed_description && (
                  <motion.section variants={itemVariants} className="card-modern p-8">
                    <h2 className="text-3xl font-bold mb-6 text-gray-900">About This Trip</h2>
                    <p className="text-gray-700 text-lg leading-relaxed">
                      {currentTrip.detailed_description}
                    </p>
                  </motion.section>
                )}

                {/* Itinerary with dropdown format */}
                {currentTrip.itinerary && Object.keys(currentTrip.itinerary).length > 0 && (
                  <motion.section variants={itemVariants} className="card-modern p-8">
                    <h2 className="text-3xl font-bold mb-6 text-gray-900 flex items-center gap-3">
                      <FileText className="w-8 h-8 text-green-600" />
                      Day-wise Itinerary
                    </h2>

                    {/* Dropdown-style itinerary */}
                    <div className="space-y-4">
                      {Object.entries(currentTrip.itinerary).map(([day, details], index) => (
                        <div key={day} className="border border-gray-200 rounded-xl overflow-hidden">
                          {/* Day Header */}
                          <button
                            onClick={() => toggleItineraryDay(index + 1)}
                            className="w-full p-6 text-left bg-gradient-to-r from-blue-50 to-green-50 hover:from-blue-100 hover:to-green-100 transition-colors duration-200"
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-4">
                                <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-green-600 rounded-full flex items-center justify-center text-white font-bold">
                                  {index + 1}
                                </div>
                                <div>
                                  <h3 className="text-xl font-semibold text-gray-900">
                                    {(details as any)?.title || `Day ${index + 1}`}
                                  </h3>
                                  <p className="text-gray-600 text-sm">
                                    Click to {expandedItineraryDays.has(index + 1) ? 'collapse' : 'expand'} details
                                  </p>
                                </div>
                              </div>
                              <div className="flex items-center gap-3">
                                {/* Transport Mode Icon */}
                                <TransportModeIcon
                                  mode={(details as any)?.transport_mode}
                                  className="w-6 h-6 text-gray-600"
                                />
                                {/* Expand/Collapse Icon */}
                                {expandedItineraryDays.has(index + 1) ? (
                                  <ChevronUp className="w-6 h-6 text-gray-400" />
                                ) : (
                                  <ChevronDown className="w-6 h-6 text-gray-400" />
                                )}
                              </div>
                            </div>
                          </button>

                          {/* Day Content */}
                          {expandedItineraryDays.has(index + 1) && (
                            <motion.div
                              initial={{ height: 0, opacity: 0 }}
                              animate={{ height: 'auto', opacity: 1 }}
                              exit={{ height: 0, opacity: 0 }}
                              transition={{ duration: 0.3 }}
                              className="overflow-hidden"
                            >
                              <div className="p-6 pt-0">
                                <div className="border-l-4 border-green-500 pl-6">
                                  <p className="text-gray-700 leading-relaxed mb-4">
                                    {(details as any)?.description}
                                  </p>

                                  {/* Additional Details */}
                                  <div className="space-y-3">
                                    {/* Accommodation */}
                                    {(details as any)?.accommodation && (
                                      <div className="flex items-center gap-2">
                                        <Bed className="w-4 h-4 text-indigo-600" />
                                        <span className="text-sm text-gray-700">
                                          <span className="font-medium">Accommodation:</span> {(details as any)?.accommodation}
                                        </span>
                                      </div>
                                    )}

                                    {/* Activities */}
                                    {(details as any)?.activities && (details as any)?.activities.length > 0 && (
                                      <div>
                                        <div className="flex items-center gap-2 mb-2">
                                          <Activity className="w-4 h-4 text-amber-600" />
                                          <span className="text-sm font-medium text-gray-700">Activities:</span>
                                        </div>
                                        <div className="flex flex-wrap gap-2 ml-6">
                                          {(details as any)?.activities.map((activity: string, actIdx: number) => (
                                            <span key={actIdx} className="text-xs bg-amber-50 text-amber-700 px-2 py-1 rounded-md">
                                              {activity}
                                            </span>
                                          ))}
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </motion.div>
                          )}
                        </div>
                      ))}
                    </div>
                  </motion.section>
                )}

                {/* Smart Tab Component for Multiple Sections */}
                {(hasLength(currentTrip.activities) || hasLength(currentTrip.optional_activities) || 
                  hasLength(currentTrip.inclusions) || hasLength(currentTrip.exclusions) || 
                  currentTrip.mode_of_travel || currentTrip.pickup_location || currentTrip.drop_location || currentTrip.property_used ||
                  hasLength(currentTrip.benefits) || hasLength(currentTrip.safety_supervision) || 
                  hasLength(currentTrip.things_to_carry)) && (
                  <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-xl">
                    <h2 className="text-3xl font-bold mb-6 text-gray-900">Trip Details</h2>
                    
                    {/* Tab Navigation - Make sure it's a single row */}
                    <div className="flex flex-wrap justify-center gap-3 sm:gap-4 mb-8 border-b pb-6 overflow-x-auto no-scrollbar">
                      <div className="flex flex-row flex-wrap justify-center w-full gap-3 sm:gap-4">
                        {tabs.map((tab) => (
                          hasTabContent(tab.id) && (
                            <button
                              key={tab.id}
                              onClick={() => setActiveTab(tab.id)}
                              className={`flex flex-col items-center p-3 sm:p-4 rounded-lg transition-all ${
                                activeTab === tab.id
                                  ? 'bg-blue-50 text-blue-600 transform scale-105 shadow-md border border-blue-200'
                                  : 'text-gray-500 hover:bg-gray-50'
                              }`}
                              aria-selected={activeTab === tab.id}
                              role="tab"
                            >
                              <div className={`${activeTab === tab.id ? 'text-blue-600' : 'text-gray-400'} mb-2 w-6 h-6`}>
                                {tab.icon}
                              </div>
                              <span className="text-xs sm:text-sm font-medium whitespace-nowrap">{tab.label}</span>
                            </button>
                          )
                        ))}
                      </div>
                    </div>

                    {/* Tab Content - with increased top padding */}
                    <div className="relative min-h-[200px] mt-6 pt-2">
                      {/* Travel Information Content */}
                      <motion.div 
                        initial="hidden"
                        animate={activeTab === 'travel-info' ? "visible" : "hidden"}
                        variants={tabContentVariants}
                        className={activeTab === 'travel-info' ? 'block' : 'hidden'}
                      >
                        <div className="grid md:grid-cols-2 gap-6">
                          {currentTrip.mode_of_travel && (
                            <div className="flex items-start gap-3">
                              <Train className="w-5 h-5 text-blue-600 mt-1 flex-shrink-0" />
                              <div>
                                <div className="font-medium text-gray-900 mb-1">Mode of Travel</div>
                                <div className="text-gray-600">{currentTrip.mode_of_travel}</div>
                              </div>
                            </div>
                          )}
                          {currentTrip.pickup_location && (
                            <div className="flex items-start gap-3">
                              <MapPin className="w-5 h-5 text-green-600 mt-1 flex-shrink-0" />
                              <div>
                                <div className="font-medium text-gray-900 mb-1">Pickup Location</div>
                                <div className="text-gray-600">{currentTrip.pickup_location}</div>
                              </div>
                            </div>
                          )}
                          {currentTrip.drop_location && (
                            <div className="flex items-start gap-3">
                              <MapPin className="w-5 h-5 text-red-600 mt-1 flex-shrink-0" />
                              <div>
                                <div className="font-medium text-gray-900 mb-1">Drop Location</div>
                                <div className="text-gray-600">{currentTrip.drop_location}</div>
                              </div>
                            </div>
                          )}
                          {currentTrip.property_used && (
                            <div className="flex items-start gap-3">
                              <Home className="w-5 h-5 text-purple-600 mt-1 flex-shrink-0" />
                              <div>
                                <div className="font-medium text-gray-900 mb-1">Accommodation</div>
                                <div className="text-gray-600">{currentTrip.property_used}</div>
                              </div>
                            </div>
                          )}
                        </div>
                      </motion.div>

                      {/* Activities Content */}
                      <motion.div 
                        initial="hidden"
                        animate={activeTab === 'activities' ? "visible" : "hidden"}
                        variants={tabContentVariants}
                        className={activeTab === 'activities' ? 'block' : 'hidden'}
                      >
                        {currentTrip.activities && currentTrip.activities.length > 0 && (
                          <div>
                            <div className="grid md:grid-cols-2 gap-4">
                              {currentTrip.activities.map((activity, index) => (
                                <div key={index} className="flex items-center gap-3">
                                  <Activity className="w-5 h-5 text-green-600" />
                                  <span className="text-gray-700">{activity}</span>
                                </div>
                              ))}
                            </div>

                            {currentTrip.optional_activities && currentTrip.optional_activities.length > 0 && (
                              <div className="mt-8">
                                <h3 className="text-xl font-semibold mb-4 text-gray-900">Optional Activities</h3>
                                <div className="grid md:grid-cols-2 gap-4">
                                  {currentTrip.optional_activities.map((activity, index) => (
                                    <div key={index} className="flex items-center gap-3">
                                      <Activity className="w-5 h-5 text-orange-600" />
                                      <span className="text-gray-700">{activity}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </motion.div>

                      {/* Inclusions & Exclusions Content */}
                      <motion.div 
                        initial="hidden"
                        animate={activeTab === 'inclusions' ? "visible" : "hidden"}
                        variants={tabContentVariants}
                        className={activeTab === 'inclusions' ? 'block' : 'hidden'}
                      >
                        <div className="grid md:grid-cols-2 gap-8">
                          {/* Inclusions */}
                          <div>
                            <h4 className="text-xl font-semibold mb-4 text-green-700 flex items-center gap-2">
                              <CheckCircle className="w-5 h-5" />
                              Included
                            </h4>
                            <div className="space-y-3">
                              {currentTrip.inclusions && currentTrip.inclusions.map((item, index) => (
                                <div key={index} className="flex items-start gap-3">
                                  <CheckCircle className="w-4 h-4 text-green-600 mt-1 flex-shrink-0" />
                                  <span className="text-gray-700">{item}</span>
                                </div>
                              ))}
                              {(!currentTrip.inclusions || currentTrip.inclusions.length === 0) && (
                                <p className="text-gray-500 italic">No inclusions specified</p>
                              )}
                            </div>
                          </div>

                          {/* Exclusions */}
                          <div>
                            <h4 className="text-xl font-semibold mb-4 text-red-700 flex items-center gap-2">
                              <XCircle className="w-5 h-5" />
                              Not Included
                            </h4>
                            <div className="space-y-3">
                              {currentTrip.exclusions && currentTrip.exclusions.map((item, index) => (
                                <div key={index} className="flex items-start gap-3">
                                  <XCircle className="w-4 h-4 text-red-600 mt-1 flex-shrink-0" />
                                  <span className="text-gray-700">{item}</span>
                                </div>
                              ))}
                              {(!currentTrip.exclusions || currentTrip.exclusions.length === 0) && (
                                <p className="text-gray-500 italic">No exclusions specified</p>
                              )}
                            </div>
                          </div>
                        </div>
                      </motion.div>

                      {/* Safety & Benefits Content */}
                      <motion.div 
                        initial="hidden"
                        animate={activeTab === 'safety' ? "visible" : "hidden"}
                        variants={tabContentVariants}
                        className={activeTab === 'safety' ? 'block' : 'hidden'}
                      >
                        <div className="grid md:grid-cols-2 gap-8">
                          {/* Benefits */}
                          {currentTrip.benefits && currentTrip.benefits.length > 0 && (
                            <div>
                              <h4 className="text-xl font-semibold mb-4 text-blue-700 flex items-center gap-2">
                                <Package className="w-5 h-5" />
                                Trip Benefits
                              </h4>
                              <div className="space-y-3">
                                {currentTrip.benefits.map((benefit, index) => (
                                  <div key={index} className="flex items-start gap-3">
                                    <CheckCircle className="w-4 h-4 text-blue-600 mt-1 flex-shrink-0" />
                                    <span className="text-gray-700">{benefit}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Safety */}
                          {currentTrip.safety_supervision && currentTrip.safety_supervision.length > 0 && (
                            <div>
                              <h4 className="text-xl font-semibold mb-4 text-green-700 flex items-center gap-2">
                                <Shield className="w-5 h-5" />
                                Safety & Supervision
                              </h4>
                              <div className="space-y-3">
                                {currentTrip.safety_supervision.map((safety, index) => (
                                  <div key={index} className="flex items-start gap-3">
                                    <Shield className="w-4 h-4 text-green-600 mt-1 flex-shrink-0" />
                                    <span className="text-gray-700">{safety}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                          {(!currentTrip.benefits || currentTrip.benefits.length === 0) && (!currentTrip.safety_supervision || currentTrip.safety_supervision.length === 0) && (
                            <div className="col-span-2 text-center py-8">
                              <p className="text-gray-500 italic">No safety information or benefits specified</p>
                            </div>
                          )}
                        </div>
                      </motion.div>

                      {/* Things to Carry Content */}
                      <motion.div 
                        initial="hidden"
                        animate={activeTab === 'things-to-carry' ? "visible" : "hidden"}
                        variants={tabContentVariants}
                        className={activeTab === 'things-to-carry' ? 'block' : 'hidden'}
                      >
                        <h4 className="text-xl font-semibold mb-4 text-amber-700 flex items-center gap-2">
                          <Backpack className="w-5 h-5" />
                          Things to Carry
                        </h4>
                        {currentTrip.things_to_carry && currentTrip.things_to_carry.length > 0 ? (
                          <div className="grid md:grid-cols-2 gap-4">
                            {currentTrip.things_to_carry.map((item, index) => (
                              <div key={index} className="flex items-start gap-3">
                                <Backpack className="w-4 h-4 text-amber-600 mt-1 flex-shrink-0" />
                                <span className="text-gray-700">{item}</span>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-8">
                            <p className="text-gray-500 italic">No items to carry specified</p>
                          </div>
                        )}
                      </motion.div>
                    </div>
                  </motion.section>
                )}

                {/* Special Notes */}
                {currentTrip.special_notes && currentTrip.special_notes.length > 0 && (
                  <motion.section variants={itemVariants} className="bg-yellow-50 border border-yellow-200 rounded-2xl p-8 shadow-xl">
                    <h2 className="text-2xl font-bold mb-6 text-yellow-800 flex items-center gap-3">
                      <Info className="w-6 h-6" />
                      Important Notes
                    </h2>
                    <div className="space-y-3">
                      {currentTrip.special_notes.map((note, index) => (
                        <div key={index} className="flex items-start gap-3">
                          <Info className="w-4 h-4 text-yellow-600 mt-1 flex-shrink-0" />
                          <span className="text-yellow-800">{note}</span>
                        </div>
                      ))}
                    </div>
                  </motion.section>
                )}

                {/* Payment Terms */}
                {currentTrip.payment_terms && (
                  <motion.section variants={itemVariants} className="bg-blue-50 border border-blue-200 rounded-2xl p-8 shadow-xl">
                    <h2 className="text-2xl font-bold mb-6 text-blue-800 flex items-center gap-3">
                      <DollarSign className="w-6 h-6" />
                      Payment Terms
                    </h2>
                    <p className="text-blue-800 leading-relaxed">
                      {currentTrip.payment_terms}
                    </p>
                  </motion.section>
                )}

                {/* Cancellation Policy */}
                {currentTrip.cancellation_policy && (
                  <motion.section variants={itemVariants} className="bg-red-50 border border-red-200 rounded-2xl p-8 shadow-xl">
                    <h2 className="text-2xl font-bold mb-6 text-red-800 flex items-center gap-3">
                      <AlertTriangle className="w-6 h-6" />
                      Cancellation Policy
                    </h2>
                    <div className="space-y-3 text-red-800">
                      {typeof currentTrip.cancellation_policy === 'object' && currentTrip.cancellation_policy !== null ? (
                        Object.entries(currentTrip.cancellation_policy).map(([key, value]) => (
                          <div key={key} className="flex justify-between items-center">
                            <span className="capitalize">{key.replace(/_/g, ' ')}:</span>
                            <span className="font-medium">{String(value)}</span>
                          </div>
                        ))
                      ) : (
                        <p>{String(currentTrip.cancellation_policy)}</p>
                      )}
                    </div>
                  </motion.section>
                )}



                {/* Booking CTA for Mobile */}
                <motion.section variants={itemVariants} className="lg:hidden bg-white rounded-2xl p-6 shadow-xl">
                  <div className="text-center">
                    <div className="text-2xl font-bold mb-2">₹{currentTrip.price_per_person.toLocaleString()}</div>
                    <div className="text-gray-600 mb-4">per person</div>

                    <Link href="/contact" className="block">
                      <Button size="lg" className="w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700">
                        Book This Trip
                      </Button>
                    </Link>
                  </div>
                </motion.section>
              </div>

              {/* Right Column - Booking Sidebar - Sticky and independently scrollable */}
              <div className="lg:col-span-1 hidden lg:block lg:pl-4">
                <div className="sticky top-6 max-h-[calc(100vh-3rem)] overflow-y-auto">
                  <motion.div variants={itemVariants} className="space-y-6">
                  {/* Pricing Card */}
                  <div className="bg-white rounded-2xl p-6 shadow-xl">
                    <div className="text-center mb-6">
                      <div className="text-3xl font-bold mb-2">₹{currentTrip.price_per_person.toLocaleString()}</div>
                      <div className="text-gray-600">per person</div>
                    </div>

                    {/* Quick Info */}
                    <div className="space-y-3 mb-6">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Duration</span>
                        <span className="font-medium">{currentTrip.days} Days, {currentTrip.nights} Nights</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Age Range</span>
                        <div className="text-gray-600">
                          {currentTrip.min_age && currentTrip.max_age 
                            ? `${currentTrip.min_age} - ${currentTrip.max_age} years` 
                            : currentTrip.min_age 
                              ? `${currentTrip.min_age}+ years` 
                              : currentTrip.max_age 
                                ? `Up to ${currentTrip.max_age} years` 
                                : 'All ages'}
                        </div>
                      </div>
                      {currentTrip.category && (
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Category</span>
                          <span className="font-medium flex items-center gap-1">
                            <Tag className="w-4 h-4" />
                            {currentTrip.category}
                          </span>
                        </div>
                      )}
                    </div>

                    <Link href="/contact" className="block">
                      <Button size="lg" className="w-full mb-4 bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700">
                        Book This Trip
                      </Button>
                    </Link>

                    {/* Contact Options */}
                    <div className="space-y-3">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full"
                        onClick={() => { window.open('tel:+917878005500', '_self'); }}
                      >
                        <Phone className="w-4 h-4 mr-2" />
                        Call: +91 78780 05500
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full"
                        onClick={() => { window.open('http://wa.me/+917878005500?text=Hi%20i%20have%20enquiry%20about%20' + encodeURIComponent(currentTrip.title) + '%20trip', '_blank'); }}
                      >
                        WhatsApp Inquiry
                      </Button>
                    </div>

                    <div className="text-center text-sm text-gray-500 mt-4">
                      <p>Contact us for group bookings and custom packages</p>
                    </div>
                  </div>

                  {/* Departure Date Calendar */}
                  {currentTrip.available_dates && currentTrip.available_dates.length > 0 && (
                    <DepartureDateCalendar
                      availableDates={currentTrip.available_dates}
                    />
                  )}

                  {/* Quick Features */}
                  <div className="bg-white rounded-2xl p-6 shadow-xl">
                    <h3 className="font-semibold text-gray-900 mb-4">Why Choose This Trip?</h3>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <Shield className="w-4 h-4 text-green-600" />
                        <span className="text-sm text-gray-700">Safety First</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Users className="w-4 h-4 text-blue-600" />
                        <span className="text-sm text-gray-700">Expert Guides</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-4 h-4 text-green-600" />
                        <span className="text-sm text-gray-700">All Inclusive</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Phone className="w-4 h-4 text-purple-600" />
                        <span className="text-sm text-gray-700">24/7 Support</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
                </div>
              </div>
            </div>

            {/* Related Trips */}
            {relatedTrips.length > 0 && (
              <motion.section variants={itemVariants} className="mt-16">
                <h2 className="text-3xl font-bold mb-8 text-gray-900">Related Trips</h2>
                <div className="grid md:grid-cols-3 gap-6">
                  {relatedTrips.map((relatedTrip) => (
                    <Link key={relatedTrip.id} href={`/trips/${relatedTrip.slug}`}>
                      <div className="bg-white rounded-lg overflow-hidden shadow-xl hover:shadow-2xl transition-shadow">
                        <div className="relative h-48">
                          <Image
                            src={relatedTrip.featured_image_url || '/images/fallback-currentTrip.jpg'}
                            alt={relatedTrip.title}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div className="p-4">
                          <h3 className="font-semibold text-gray-900 mb-2">{relatedTrip.title}</h3>
                          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                            {relatedTrip.description || 'Discover amazing experiences'}
                          </p>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-500">{relatedTrip.days} Days, {relatedTrip.nights} Nights</span>
                            <span className="font-semibold text-primary-600">₹{relatedTrip.price_per_person.toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </motion.section>
            )}


          </div>
        </motion.div>
      </div>
    </>
  );
}
