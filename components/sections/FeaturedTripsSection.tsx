'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  MapPin,
  Calendar,
  Users,
  Star,
  ArrowRight,
  Mountain,
  RefreshCw
} from 'lucide-react';
import { cn, getDifficultyColor } from '@/lib/utils';
import type { Trip } from '@/types/database';
import { usePublicTrips } from '@/hooks/useTrips';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface FeaturedTrip {
  id: string;
  title: string;
  slug: string;
  description: string | null;
  destination: string;
  days: number;
  nights: number;
  price_per_person: number;
  difficulty: 'easy' | 'moderate' | 'challenging' | 'extreme';
  featured_image_url: string | null;
  is_featured: boolean;
  is_active: boolean;
}

// Minimal trip interface for homepage use
interface MinimalTrip {
  id: string;
  title: string;
  slug: string;
  description: string;
  destination: string;
  days: number;
  nights: number;
  price_per_person: number;
  difficulty: string;
  featured_image_url: string;
  is_featured: boolean;
  is_active: boolean;
}

interface FeaturedTripsSectionProps {
  initialFeaturedTrips?: MinimalTrip[];
}

export default function FeaturedTripsSection({ initialFeaturedTrips = [] }: FeaturedTripsSectionProps) {
  const [hoveredTrip, setHoveredTrip] = useState<string | null>(null);

  // Use React Query to fetch featured trips with real-time updates
  const { data: tripsData, isLoading, error, refetch } = usePublicTrips({
    limit: 20 // Get more trips to ensure we have featured ones
  });

  // Use fetched data if available, otherwise fall back to initial data
  const allTrips = tripsData?.data || initialFeaturedTrips;

  // Filter for featured trips
  const featuredTrips = allTrips.filter((trip: Trip) => trip.is_featured);
  const isFallbackMode = featuredTrips.length === 0;

  // Loading state
  if (isLoading && initialFeaturedTrips.length === 0) {
    return (
      <section className="section-padding bg-gray-50">
        <div className="container-custom">
          <div className="flex items-center justify-center py-20">
            <LoadingSpinner size="large" />
          </div>
        </div>
      </section>
    );
  }

  // Error state
  if (error && featuredTrips.length === 0) {
    return (
      <section className="section-padding bg-gray-50">
        <div className="container-custom">
          <div className="text-center py-20">
            <div className="text-gray-400 mb-4">
              <Mountain className="w-16 h-16 mx-auto" />
            </div>
            <h3 className="text-xl font-medium text-gray-900 mb-2">Failed to load featured trips</h3>
            <p className="text-gray-600 mb-4">
              There was an error loading the featured trips. Please try again.
            </p>
            <button
              onClick={() => refetch()}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </button>
          </div>
        </div>
      </section>
    );
  }

  // If there are no trips, don't render this section at all
  if (isFallbackMode) {
    return null;
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  return (
    <section id="featured-trips-section" className="section-padding bg-gray-50">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="text-center mb-20"
        >
          <motion.h2
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-5xl md:text-6xl lg:text-7xl font-display font-black text-gray-900 mb-6"
          >
            Featured{' '}
            <span className="bg-gradient-to-r from-coral-500 via-orange-500 to-teal-500 bg-clip-text text-transparent">
              Destinations
            </span>
          </motion.h2>
          <motion.div
            initial={{ width: 0 }}
            whileInView={{ width: "120px" }}
            viewport={{ once: true }}
            transition={{ duration: 1, delay: 0.4 }}
            className="h-1.5 bg-gradient-to-r from-coral-400 to-teal-400 rounded-full mx-auto mb-8"
          />
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light"
          >
            Discover our most popular educational tours and adventure experiences.
            Each destination is carefully selected to provide meaningful learning opportunities
            and unforgettable memories.
          </motion.p>
        </motion.div>

        {/* Trips Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {featuredTrips.map((trip: Trip | MinimalTrip, index: number) => (
            <motion.div
              key={trip.id}
              variants={itemVariants}
              onMouseEnter={() => setHoveredTrip(trip.id)}
              onMouseLeave={() => setHoveredTrip(null)}
              className="group"
            >
              <div className="relative bg-white rounded-3xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-700 transform hover:-translate-y-3 hover:rotate-1 border border-gray-100">
                {/* Image */}
                <div className="relative h-72 overflow-hidden">
                  <Image
                    src={trip.featured_image_url || '/images/fallback-trip.jpg'}
                    alt={trip.title}
                    fill
                    className="object-cover transition-transform duration-1000 group-hover:scale-125"
                  />

                  {/* Gradient Overlays */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
                  <div className="absolute inset-0 bg-gradient-to-r from-coral-500/20 via-transparent to-teal-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  {/* Difficulty Badge */}
                  <div className="absolute top-6 right-6">
                    <span className={cn(
                      'px-4 py-2 rounded-2xl text-sm font-bold backdrop-blur-md border border-white/20 shadow-lg',
                      getDifficultyColor(trip.difficulty)
                    )}>
                      {trip.difficulty.charAt(0).toUpperCase() + trip.difficulty.slice(1)}
                    </span>
                  </div>

                  {/* Floating Elements */}
                  <div className="absolute top-6 left-6 opacity-0 group-hover:opacity-100 transition-all duration-500 delay-200">
                    <div className="w-3 h-3 bg-secondary-400 rounded-full animate-float" />
                  </div>

                  {/* Quick Info Overlay */}
                  <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{
                      opacity: hoveredTrip === trip.id ? 1 : 0,
                      y: hoveredTrip === trip.id ? 0 : 30
                    }}
                    transition={{ duration: 0.4, ease: "easeOut" }}
                    className="absolute bottom-6 left-6 right-6"
                  >
                    <div className="bg-black/20 backdrop-blur-xl rounded-2xl p-4 border border-white/20 shadow-2xl">
                      <div className="flex items-center justify-between text-white">
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-5 w-5 text-secondary-300" />
                          <span className="font-semibold">{trip.days} Days, {trip.nights} Nights</span>
                        </div>
                        <div className="w-2 h-2 bg-coral-400 rounded-full animate-pulse" />
                      </div>
                    </div>
                  </motion.div>
                </div>

                {/* Content */}
                <div className="p-8">
                  <div className="mb-4">
                    <h3 className="text-2xl font-bold text-gray-900 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-coral-600 group-hover:to-teal-600 group-hover:bg-clip-text transition-all duration-500 leading-tight">
                      {trip.title}
                    </h3>
                    <div className="w-12 h-1 bg-gradient-to-r from-coral-400 to-teal-400 rounded-full mt-2 group-hover:w-20 transition-all duration-500" />
                  </div>

                  <div className="flex items-center space-x-2 mb-4 text-gray-600">
                    <MapPin className="h-5 w-5 text-coral-500" />
                    <span className="text-base font-medium">{trip.destination}</span>
                  </div>

                  <p className="text-gray-600 mb-6 line-clamp-3 leading-relaxed text-base">
                    {trip.description || 'Discover amazing educational experiences and create unforgettable memories.'}
                  </p>

                  {/* Trip Details */}
                  <div className="flex items-center justify-between mb-8 text-sm text-gray-500">
                    <div className="flex items-center space-x-2 bg-gray-50 rounded-xl px-3 py-2">
                      <Calendar className="h-4 w-4 text-coral-500" />
                      <span className="font-medium">{trip.days} Days, {trip.nights} Nights</span>
                    </div>
                  </div>

                  {/* CTA */}
                  <Link
                    href={`/trips/${trip.slug}`}
                    className="group/btn relative inline-flex items-center justify-center w-full px-8 py-4 bg-gradient-to-r from-coral-500 to-orange-500 text-white font-bold rounded-2xl hover:from-coral-600 hover:to-orange-600 transition-all duration-500 transform hover:scale-105 hover:shadow-xl hover:shadow-coral-500/25 overflow-hidden"
                  >
                    <div className="absolute inset-0 bg-shimmer-gradient animate-shimmer opacity-0 group-hover/btn:opacity-100 transition-opacity duration-500" />
                    <span className="relative z-10 font-bold">Explore Trip</span>
                    <ArrowRight className="relative z-10 ml-3 h-5 w-5 group-hover/btn:translate-x-2 transition-transform duration-300" />
                  </Link>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
