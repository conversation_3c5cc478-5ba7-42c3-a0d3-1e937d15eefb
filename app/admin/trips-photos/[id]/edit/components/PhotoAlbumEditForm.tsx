'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import CloudinaryUpload from '@/components/ui/CloudinaryUpload';
import ConfirmationModal from '@/components/ui/ConfirmationModal';
import { PhotoAlbum, UpdatePhotoAlbumRequest } from '@/types/photo-album';
import { useToast } from '@/hooks/useToast';
import { ArrowLeft, Camera, Save, Loader2, Trash2 } from 'lucide-react';
import { useUpdatePhotoAlbum, useDeletePhotoAlbum } from '@/hooks/usePhotoAlbums';

interface PhotoAlbumEditFormProps {
  album: PhotoAlbum;
}

export default function PhotoAlbumEditForm({ album }: PhotoAlbumEditFormProps) {
  const router = useRouter();
  const toast = useToast();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [formData, setFormData] = useState<UpdatePhotoAlbumRequest>({
    trip_name: album.trip_name,
    trip_description: album.trip_description || '',
    featured_image_url: album.featured_image_url || '',
    access_password: '', // Always start empty since we can't show hashed passwords
    manual_shareable_url: album.manual_shareable_url || '',
  });

  const updatePhotoAlbumMutation = useUpdatePhotoAlbum();
  const deletePhotoAlbumMutation = useDeletePhotoAlbum();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleImageUpload = (url: string) => {
    setFormData(prev => ({
      ...prev,
      featured_image_url: url
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.trip_name?.trim()) {
      toast.error('Trip name is required');
      return;
    }

    try {
      await updatePhotoAlbumMutation.mutateAsync({
        id: album.id,
        data: formData,
      });
      toast.success('Album updated successfully!');
      router.push(`/admin/trips-photos/${album.id}`);
    } catch (error) {
      console.error('Error updating album:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update album');
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deletePhotoAlbumMutation.mutateAsync(album.id);
      toast.success('Album deleted successfully');
      router.push('/admin/trips-photos');
    } catch (error) {
      console.error('Error deleting album:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete album');
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <Link
          href={`/admin/trips-photos/${album.id}`}
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Album
        </Link>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mr-4">
              <Camera className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Edit Photo Album</h1>
              <p className="text-gray-600">Update album details and settings</p>
            </div>
          </div>
          <button
            onClick={handleDeleteClick}
            disabled={deletePhotoAlbumMutation.isPending}
            className="inline-flex items-center px-4 py-2 border border-red-300 shadow-sm text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            {deletePhotoAlbumMutation.isPending ? 'Deleting...' : 'Delete Album'}
          </button>
        </div>
      </div>

      {/* Form */}
      <div className="bg-white shadow-sm rounded-lg">
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Trip Name */}
          <div>
            <label htmlFor="trip_name" className="block text-sm font-medium text-gray-700 mb-1">
              Trip Name *
            </label>
            <input
              type="text"
              id="trip_name"
              name="trip_name"
              value={formData.trip_name}
              onChange={handleInputChange}
              required
              placeholder="Enter trip name"
              className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
            />
          </div>

          {/* Trip Description */}
          <div>
            <label htmlFor="trip_description" className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              id="trip_description"
              name="trip_description"
              value={formData.trip_description}
              onChange={handleInputChange}
              rows={3}
              placeholder="Brief description of the trip..."
              className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
            />
          </div>

          {/* Featured Image */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Featured Image
            </label>
            <CloudinaryUpload
              onUpload={handleImageUpload}
              currentImage={formData.featured_image_url}
              folder="positive7/trips"
            />
          </div>

          {/* Access Password */}
          <div>
            <label htmlFor="access_password" className="block text-sm font-medium text-gray-700 mb-1">
              {album.access_password_hash ? 'Change Access Password' : 'Set Access Password (Optional)'}
            </label>

            {/* Current Password Status */}
            {album.access_password_hash && (
              <div className="mb-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium text-green-800">Password Protection Active</span>
                </div>
                <p className="text-xs text-green-700 mt-1">
                  This album is currently protected with a password. Users need the correct password to access it.
                </p>
              </div>
            )}

            <input
              type="password"
              id="access_password"
              name="access_password"
              value={formData.access_password}
              onChange={handleInputChange}
              placeholder={album.access_password_hash ? "Enter new password to change" : "Leave empty for public access"}
              className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
            />

            <div className="text-sm mt-1 space-y-2">
              {album.access_password_hash ? (
                <div className="space-y-1 text-gray-600">
                  <p>• Enter a new password to change the current one</p>
                  <p>• Leave empty to remove password protection entirely</p>
                  <p className="text-xs text-gray-400">Note: For security reasons, the current password cannot be displayed</p>
                </div>
              ) : (
                <div className="space-y-1 text-gray-600">
                  <p>• If set, users will need this password to view the album on the public site</p>
                  <p>• Minimum 4 characters required</p>
                </div>
              )}

              {/* Password Security Warning */}
              <div className="p-2 bg-amber-50 border border-amber-200 rounded text-xs">
                <div className="flex items-start space-x-1">
                  <span className="text-amber-600">⚠️</span>
                  <div className="text-amber-700">
                    <strong>Security Reminder:</strong> Write down any new password immediately.
                    Passwords are encrypted and cannot be recovered if forgotten.
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Manual Shareable URL */}
          <div>
            <label htmlFor="manual_shareable_url" className="block text-sm font-medium text-gray-700 mb-1">
              Manual Shareable Link
            </label>
            <input
              type="url"
              id="manual_shareable_url"
              name="manual_shareable_url"
              value={formData.manual_shareable_url}
              onChange={handleInputChange}
              placeholder="https://photos.app.goo.gl/... or https://photos.google.com/share/..."
              className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
            />
            <p className="text-sm text-gray-500 mt-1">
              Update the Google Photos shareable link for public access. Supports both photos.app.goo.gl and photos.google.com/share formats.
            </p>
          </div>

          {/* Google Photos Info */}
          <div className="space-y-4">
            {album.oauth_user_email && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-start">
                  <Camera className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                  <div>
                    <h4 className="text-sm font-medium text-green-900">OAuth Integration</h4>
                    <div className="text-sm text-green-700 mt-1 space-y-1">
                      <p>✅ Connected to Google Photos account: {album.oauth_user_email}</p>
                      {album.google_photos_album_id && (
                        <p>✅ Album ID: {album.google_photos_album_id}</p>
                      )}

                    </div>
                  </div>
                </div>
              </div>
            )}

            {album.manual_shareable_url && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start">
                  <Camera className="h-5 w-5 text-blue-600 mt-0.5 mr-3" />
                  <div>
                    <h4 className="text-sm font-medium text-blue-900">Public Shareable Link</h4>
                    <div className="text-sm text-blue-700 mt-1 space-y-1">
                      <p>✅ Manual shareable link configured</p>
                      <p>
                        <a
                          href={album.manual_shareable_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 underline"
                        >
                          View public album link
                        </a>
                      </p>
                      <p className="text-xs text-blue-600">This link is used on the public trips-photos page</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <Link
              href={`/admin/trips-photos/${album.id}`}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={updatePhotoAlbumMutation.isPending || !formData.trip_name?.trim()}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {updatePhotoAlbumMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Update Album
                </>
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteModal}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Trip Photo Album"
        message={`Are you sure you want to delete "${album.trip_name}"?\n\nThis action cannot be undone and will remove the album from the database. Note: Due to Google Photos API limitations, you will need to manually delete the album from Google Photos if it exists.`}
        confirmText="Delete Album"
        variant="danger"
        loading={deletePhotoAlbumMutation.isPending}
      />
    </div>
  );
}
