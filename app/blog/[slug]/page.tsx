import type { Metadata } from 'next/types'
import { notFound } from 'next/navigation'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import BlogPostClient from '@/components/blog/BlogPostClient'
import { createServerSupabase } from '@/lib/supabase-server'

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic'
export const revalidate = 0

interface BlogPostPageProps {
  params: Promise<{
    slug: string
  }>
}

// Generate metadata for the blog post
export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const { slug } = await params;
  const supabase = createServerSupabase()
  
  const { data: post } = await supabase
    .from('blog_posts')
    .select('title, excerpt, featured_image_url, created_at, author_id')
    .eq('slug', slug)
    .eq('is_published', true)
    .single()

  if (!post) {
    return {
      title: 'Blog Post Not Found - Positive7',
      description: 'The requested blog post could not be found.'
    }
  }

  return {
    title: `${post.title} - Positive7 Blog`,
    description: post.excerpt || `Read ${post.title} on Positive7 blog`,
    keywords: `${post.title}, Positive7, educational tours, travel blog`,
    openGraph: {
      title: post.title,
      description: post.excerpt || '',
      images: post.featured_image_url ? [post.featured_image_url] : [],
      type: 'article',
      publishedTime: post.created_at || undefined
    },
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: post.excerpt || '',
      images: post.featured_image_url ? [post.featured_image_url] : []
    }
  }
}

// Generate static params for all published blog posts
export async function generateStaticParams() {
  const supabase = createServerSupabase()
  
  const { data: posts } = await supabase
    .from('blog_posts')
    .select('slug')
    .eq('is_published', true)

  return posts?.map((post) => ({
    slug: post.slug,
  })) || []
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = await params;
  const supabase = createServerSupabase()

  // Fetch the blog post
  const { data: post, error } = await supabase
    .from('blog_posts')
    .select(`
      id,
      title,
      slug,
      content,
      excerpt,
      featured_image_url,
      author_id,
      created_at,
      updated_at,
      category,
      tags,
      published_at,
      is_published,
      seo_title,
      seo_description
    `)
    .eq('slug', slug)
    .eq('is_published', true)
    .single()

  // Handle different error cases
  if (error) {
    // PGRST116 is the specific "no rows returned" error from PostgREST
    if (error.code === 'PGRST116') {
      notFound()
    }
    // Handle other database errors
    console.error('Database error fetching blog post:', error);
    notFound()
  }

  // Handle case where no post is returned (shouldn't happen with proper error handling above)
  if (!post) {
    notFound()
  }

  // Calculate reading time (rough estimate: 200 words per minute)
  const wordCount = post.content.split(/\s+/).length
  const readingTime = Math.ceil(wordCount / 200)

  // Use current date as fallback for created_at
  const currentDate = new Date().toISOString()

  // Use the post data directly since it matches the BlogPost interface
  const adaptedPost = post

  // Fetch related posts
  const { data: relatedPostsData } = await supabase
    .from('blog_posts')
    .select(`
      id, 
      title, 
      slug, 
      excerpt, 
      featured_image_url, 
      author_id,
      created_at, 
      category
    `)
    .eq('is_published', true)
    .neq('id', post.id)
    .eq('category', post.category || 'Uncategorized')
    .limit(3)

  // Adapt related posts to match expected structure
  const relatedPosts = relatedPostsData?.map(post => ({
    id: post.id,
    title: post.title,
    slug: post.slug,
    excerpt: post.excerpt || '',
    featured_image_url: post.featured_image_url, // Keep as string | null
    created_at: post.created_at,
    category: post.category,
  })) || []

  return (
    <>
      <Header />
      <main className="flex-1">
        <BlogPostClient
          blogSlug={slug}
          initialPost={adaptedPost}
          relatedPosts={relatedPosts}
        />
      </main>
      <Footer />
    </>
  )
}
