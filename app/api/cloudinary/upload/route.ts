import { NextRequest, NextResponse } from 'next/server';
import { uploadToCloudinary, trackIncompleteUpload } from '@/lib/cloudinary';
import { verifyAdminAccess } from '@/lib/auth-server';

// POST /api/cloudinary/upload - Upload file to Cloudinary
// NOTE: No rate limiting applied - admins need to upload large batches of images (200-250 at a time)
// Admin authentication provides sufficient protection against abuse
export async function POST(request: NextRequest) {
  try {
    // Verify admin access for uploads
    const { user, hasAccess } = await verifyAdminAccess();

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('file');
    const folder = formData.get('folder') as string;
    const public_id = formData.get('public_id') as string;
    const transformation = formData.get('transformation') as string;
    const resource_type = formData.get('resource_type') as string;

    // Check if file is a File-like object
    if (!file || typeof file !== 'object' || !('type' in file) || !('size' in file) || !('arrayBuffer' in file)) {
      return NextResponse.json(
        { error: 'No valid file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
    if (!allowedTypes.includes(file.type as string)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPEG, PNG, WebP, and GIF are allowed.' },
        { status: 400 }
      );
    }

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if ((file.size as number) > maxSize) {
      return NextResponse.json(
        { error: 'File too large. Maximum size is 10MB.' },
        { status: 400 }
      );
    }

    // Upload options
    const uploadOptions: any = {
      folder: folder || 'positive7',
      resource_type: resource_type || 'auto',
    };

    if (public_id) uploadOptions.public_id = public_id;
    if (transformation) {
      try {
        uploadOptions.transformation = JSON.parse(transformation);
      } catch (e) {
        // If transformation is not valid JSON, ignore it
      }
    }

    // Upload to Cloudinary
    const result = await uploadToCloudinary(file, uploadOptions);

    if (!result.success) {
      console.error(`❌ Upload failed for file: ${file.name || 'unknown'}, folder: ${uploadOptions.folder}`, result.error);
      return NextResponse.json(
        { error: result.error || 'Upload failed' },
        { status: 500 }
      );
    }

    // Track as incomplete upload for potential cleanup
    if (result.public_id) {
      const context = `upload-${uploadOptions.folder || 'general'}`;
      trackIncompleteUpload(result.public_id, context, uploadOptions.folder || 'positive7');
    }

    console.log(`✅ File uploaded successfully: ${result.public_id} (${result.bytes} bytes)`);

    return NextResponse.json({
      message: 'File uploaded successfully',
      url: result.url,
      public_id: result.public_id,
      width: result.width,
      height: result.height,
      format: result.format,
      bytes: result.bytes,
      version: result.version,
      resource_type: result.resource_type,
    });

  } catch (error: any) {
    console.error('Cloudinary upload error:', error);
    return NextResponse.json(
      { error: 'Failed to upload file', details: error.message },
      { status: 500 }
    );
  }
}
