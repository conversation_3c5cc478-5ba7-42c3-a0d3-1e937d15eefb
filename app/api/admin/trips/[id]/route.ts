import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminClient } from '@/lib/auth-server';
import { logTripCrud, crudAuditLogger, CrudAuditLogger } from '@/lib/crud-audit-logger';
import { handleApiError } from '@/lib/error-handler';
import { deleteFromCloudinary, extractCloudinaryPublicId, markUploadComplete } from '@/lib/cloudinary';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

// GET /api/admin/trips/[id] - Get a single trip
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('trips', 'read');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    // Ensure params.id is available before using it
    const { id } = await context.params;
    if (!id) {
      return NextResponse.json(
        { error: 'Trip ID is required' },
        { status: 400 }
      );
    }

    const supabase = createAdminClient();
    const { data: trip, error } = await supabase
      .from('trips')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching trip:', error);
      return NextResponse.json(
        { error: 'Failed to fetch trip' },
        { status: 500 }
      );
    }

    if (!trip) {
      return NextResponse.json(
        { error: 'Trip not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ data: trip });
  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

// PUT /api/admin/trips/[id] - Update a trip
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('trips', 'update');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    // Ensure params.id is available before using it
    const { id } = await context.params;
    if (!id) {
      return NextResponse.json(
        { error: 'Trip ID is required' },
        { status: 400 }
      );
    }

    const supabase = createAdminClient();
    const body = await request.json();

    // Get original trip data for change tracking
    const { data: originalTrip } = await supabase
      .from('trips')
      .select('*')
      .eq('id', id)
      .single();

    // Basic validation
    const requiredFields = ['title', 'description', 'destination', 'days', 'nights', 'price_per_person', 'difficulty'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Store old image URL for cleanup after successful DB update
    let oldImageToDelete: string | null = null;
    if (body.featured_image_url && originalTrip?.featured_image_url &&
        body.featured_image_url !== originalTrip.featured_image_url) {
      oldImageToDelete = originalTrip.featured_image_url;
    }

    // Validate nights = days - 1
    const expectedNights = Math.max(0, body.days - 1);
    if (body.nights !== expectedNights) {
      return NextResponse.json(
        { error: `Nights must be exactly ${expectedNights} for ${body.days} day${body.days > 1 ? 's' : ''}` },
        { status: 400 }
      );
    }

    // Generate new slug if title changed
    if (body.title) {
      body.slug = body.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
    }

    // Update trip in database
    const { data: trip, error: dbError } = await supabase
      .from('trips')
      .update({
        ...body,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (dbError) {
      console.error('Error updating trip:', dbError);

      // Log failed update
      await crudAuditLogger.logFailedOperation(
        'update',
        'trip',
        id,
        user.id,
        user.email || user.username || 'Unknown',
        dbError.message,
        request
      );

      return NextResponse.json(
        { error: 'Failed to update trip' },
        { status: 500 }
      );
    }

    // Track changes for audit log using utility function
    const changes = originalTrip ? CrudAuditLogger.calculateChanges(originalTrip, body) : {};

    // Log successful update
    await logTripCrud.update(
      trip.id,
      trip.title,
      user.id,
      user.email || user.username || 'Unknown',
      changes,
      request
    );

    // Mark new image as complete and clean up old image from Cloudinary after successful DB update (non-blocking)
    if (body.featured_image_url) {
      const newPublicId = extractCloudinaryPublicId(body.featured_image_url);
      if (newPublicId) {
        markUploadComplete(newPublicId);
      }
    }

    if (oldImageToDelete) {
      const oldPublicId = extractCloudinaryPublicId(oldImageToDelete);
      if (oldPublicId) {
        // Fire and forget - don't block the response
        deleteFromCloudinary(oldPublicId, 'image', 'trip-replace').catch(cloudinaryError => {
          console.error('Error deleting old trip featured image from Cloudinary:', cloudinaryError);
        });
      }
    }

    return NextResponse.json({ data: trip });
  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

// DELETE /api/admin/trips/[id] - Delete a trip
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('trips', 'delete');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    // Ensure params.id is available before using it
    const { id } = await context.params;
    if (!id) {
      return NextResponse.json(
        { error: 'Trip ID is required' },
        { status: 400 }
      );
    }

    const supabase = createAdminClient();

    // Get trip data before deletion for audit log and Cloudinary cleanup
    const { data: tripToDelete } = await supabase
      .from('trips')
      .select('title, featured_image_url')
      .eq('id', id)
      .single();

    const { error } = await supabase
      .from('trips')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting trip:', error);

      // Log failed deletion
      await crudAuditLogger.logFailedOperation(
        'delete',
        'trip',
        id,
        user.id,
        user.email || user.username || 'Unknown',
        error.message,
        request
      );

      return NextResponse.json(
        { error: 'Failed to delete trip' },
        { status: 500 }
      );
    }

    // Log successful deletion
    await logTripCrud.delete(
      id,
      tripToDelete?.title || 'Unknown Trip',
      user.id,
      user.email || user.username || 'Unknown',
      request
    );

    // Delete featured image from Cloudinary after successful DB deletion (non-blocking)
    if (tripToDelete?.featured_image_url) {
      const publicId = extractCloudinaryPublicId(tripToDelete.featured_image_url);
      if (publicId) {
        // Fire and forget - don't block the response
        deleteFromCloudinary(publicId, 'image', 'trip-delete').catch(cloudinaryError => {
          console.error('Error deleting trip featured image from Cloudinary:', cloudinaryError);
        });
      }
    }

    return NextResponse.json({ message: 'Trip deleted successfully' });
  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
} 