import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess } from '@/lib/auth-server';
import { cleanupIncompleteUploads } from '@/lib/cloudinary';
import { handleApiError } from '@/lib/error-handler';

// POST /api/admin/cloudinary/cleanup - Cleanup incomplete uploads
export async function POST(request: NextRequest) {
  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess();

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const olderThanMinutes = body.olderThanMinutes || 30;

    // Validate input
    if (typeof olderThanMinutes !== 'number' || olderThanMinutes < 1 || olderThanMinutes > 1440) {
      return NextResponse.json(
        { error: 'olderThanMinutes must be a number between 1 and 1440 (24 hours)' },
        { status: 400 }
      );
    }

    console.log(`🧹 Admin ${user.username || user.email} initiated cleanup of incomplete uploads older than ${olderThanMinutes} minutes`);

    const result = await cleanupIncompleteUploads(olderThanMinutes);

    return NextResponse.json({
      success: true,
      cleaned: result.cleaned,
      errors: result.errors,
      message: `Cleaned up ${result.cleaned} incomplete uploads${result.errors.length > 0 ? ` with ${result.errors.length} errors` : ''}`
    });

  } catch (error: any) {
    const appError = handleApiError(error);
    console.error('❌ Error in cleanup endpoint:', appError);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

// GET /api/admin/cloudinary/cleanup - Get cleanup status (for monitoring)
export async function GET(request: NextRequest) {
  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess();

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    // This would require implementing a way to get current incomplete uploads count
    // For now, return basic info
    return NextResponse.json({
      message: 'Cleanup endpoint is available',
      usage: 'POST with { "olderThanMinutes": 30 } to cleanup incomplete uploads',
      defaultCleanupAge: 30,
      maxCleanupAge: 1440
    });

  } catch (error: any) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}
