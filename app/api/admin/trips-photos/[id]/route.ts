import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';
import { hashPassword } from '@/lib/security';
import { deleteFromCloudinary, extractCloudinaryPublicId } from '@/lib/cloudinary';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

// GET /api/admin/trips-photos/[id] - Get a single trip photo details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Destructure id from params to avoid direct property access
    const { id } = await params;
    const supabase = createServerSupabase();

    const { data, error } = await supabase
      .from('trip_photos_details')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Trip photo details not found' }, { status: 404 });
      }
      throw error;
    }

    return NextResponse.json({ data });
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'An error occurred fetching trip photo details' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/trips-photos/[id] - Update a trip photo details
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = createServerSupabase();
    // Destructure id from params to avoid direct property access
    const { id } = await params;
    const body = await request.json();

    // Basic validation
    if (!body.trip_name) {
      return NextResponse.json(
        { error: 'Trip name is required' },
        { status: 400 }
      );
    }

    // Get original data for Cloudinary cleanup
    const { data: originalData } = await supabase
      .from('trip_photos_details')
      .select('featured_image_url')
      .eq('id', id)
      .single();

    // Store old image URL for cleanup after successful DB update
    let oldImageToDelete: string | null = null;
    if (body.featured_image_url && originalData?.featured_image_url &&
        body.featured_image_url !== originalData.featured_image_url) {
      oldImageToDelete = originalData.featured_image_url;
    }

    // Prepare update data
    const updateData: any = {
      ...body,
      updated_at: new Date().toISOString(),
    };

    // Ensure only Google Photos OAuth is supported
    if (updateData.storage_type && updateData.storage_type !== 'google_photos_oauth') {
      updateData.storage_type = 'google_photos_oauth';
    }

    // Don't update OAuth fields through this route (they're managed by OAuth flow)
    delete updateData.oauth_refresh_token;
    delete updateData.oauth_refresh_token_encrypted;
    delete updateData.google_photos_album_id;

    // Handle password - hash if provided, clear if empty
    if (body.access_password !== undefined) {
      const trimmedPassword = body.access_password?.trim();
      if (trimmedPassword) {
        // Validate password length
        if (trimmedPassword.length < 4) {
          return NextResponse.json(
            { error: 'Password must be at least 4 characters long' },
            { status: 400 }
          );
        }
        // Hash the password and store in access_password_hash
        updateData.access_password_hash = await hashPassword(trimmedPassword);
      } else {
        // Clear password field if empty password provided
        updateData.access_password_hash = null;
      }
      // Remove the plaintext password from update data
      delete updateData.access_password;
    }

    // Update trip photo details
    const { data, error } = await supabase
      .from('trip_photos_details')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating trip photo details:', error);
      return NextResponse.json(
        { error: 'Failed to update trip photo details' },
        { status: 500 }
      );
    }

    // Clean up old image from Cloudinary after successful DB update (non-blocking)
    if (oldImageToDelete) {
      const oldPublicId = extractCloudinaryPublicId(oldImageToDelete);
      if (oldPublicId) {
        // Fire and forget - don't block the response
        deleteFromCloudinary(oldPublicId).catch(cloudinaryError => {
          console.error('Error deleting old trip photos featured image from Cloudinary:', cloudinaryError);
        });
      }
    }

    return NextResponse.json({ data });
  } catch (error) {
    console.error('Error in PUT /api/admin/trips-photos/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/trips-photos/[id] - Delete a trip photo details and Google Photos album
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Destructure id from params to avoid direct property access
    const { id } = await params;
    const supabase = createServerSupabase();

    // First, get the album details to check for Google Photos integration and Cloudinary cleanup
    const { data: albumData, error: fetchError } = await supabase
      .from('trip_photos_details')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError) {
      console.error('[DELETE] Error fetching album data:', fetchError);
      return NextResponse.json(
        { error: 'Album not found' },
        { status: 404 }
      );
    }

    // If this is a Google Photos OAuth album, attempt to delete the album from Google Photos
    if (albumData?.storage_type === 'google_photos_oauth' &&
        albumData?.google_photos_album_id &&
        albumData?.oauth_refresh_token) {

      console.log(`[DELETE] Attempting to delete Google Photos album: ${albumData.google_photos_album_id}`);

      try {
        // Import Google Photos API functions
        const { deleteGooglePhotosAlbum } = await import('@/lib/google-photos-api');

        // Attempt to delete the album from Google Photos
        await deleteGooglePhotosAlbum(
          albumData.oauth_refresh_token,
          albumData.google_photos_album_id
        );

        console.log(`[DELETE] Successfully deleted Google Photos album: ${albumData.google_photos_album_id}`);
      } catch (photosError) {
        console.error('[DELETE] Error deleting Google Photos album:', photosError);
        // Continue with database deletion even if Google Photos deletion fails
        // This prevents orphaned database records
        console.log('[DELETE] Continuing with database deletion despite Google Photos error');
      }
    }

    // Delete featured image from Cloudinary if it exists
    if (albumData.featured_image_url) {
      const publicId = extractCloudinaryPublicId(albumData.featured_image_url);
      if (publicId) {
        try {
          await deleteFromCloudinary(publicId);
        } catch (cloudinaryError) {
          console.error('Error deleting trip photos featured image from Cloudinary:', cloudinaryError);
          // Continue with database deletion even if Cloudinary deletion fails
        }
      }
    }

    // Delete the database record
    const { error: deleteError } = await supabase
      .from('trip_photos_details')
      .delete()
      .eq('id', id);

    if (deleteError) {
      console.error('[DELETE] Error deleting from database:', deleteError);
      throw deleteError;
    }

    console.log(`[DELETE] Successfully deleted album: ${id}`);
    return NextResponse.json({
      success: true,
      message: 'Album and associated Google Photos album deleted successfully'
    });

  } catch (error: any) {
    console.error('[DELETE] Delete operation failed:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred deleting the album' },
      { status: 500 }
    );
  }
}