import { cleanupIncompleteUploads } from './cloudinary';

// Scheduled cleanup function that can be called by cron jobs or other schedulers
export async function runScheduledCleanup(olderThanMinutes: number = 60) {
  console.log(`🕐 Starting scheduled Cloudinary cleanup (older than ${olderThanMinutes} minutes)`);
  
  try {
    const result = await cleanupIncompleteUploads(olderThanMinutes);
    
    if (result.cleaned > 0 || result.errors.length > 0) {
      console.log(`🧹 Scheduled cleanup completed: ${result.cleaned} cleaned, ${result.errors.length} errors`);
      
      if (result.errors.length > 0) {
        console.error('❌ Cleanup errors:', result.errors);
      }
    } else {
      console.log('✅ No incomplete uploads to cleanup');
    }
    
    return result;
  } catch (error) {
    console.error('❌ Scheduled cleanup failed:', error);
    throw error;
  }
}

// Helper function to run cleanup with different age thresholds
export async function runTieredCleanup() {
  console.log('🔄 Starting tiered cleanup process');
  
  const results = {
    recent: await runScheduledCleanup(30),    // 30 minutes
    medium: await runScheduledCleanup(120),   // 2 hours  
    old: await runScheduledCleanup(720),      // 12 hours
  };
  
  const totalCleaned = results.recent.cleaned + results.medium.cleaned + results.old.cleaned;
  const totalErrors = results.recent.errors.length + results.medium.errors.length + results.old.errors.length;
  
  console.log(`🏁 Tiered cleanup completed: ${totalCleaned} total cleaned, ${totalErrors} total errors`);
  
  return {
    totalCleaned,
    totalErrors,
    details: results
  };
}
